{"roots": ["hadith_iq"], "packages": [{"name": "hadith_iq", "version": "1.0.0+1", "dependencies": ["file_picker", "fl_chart", "flutter", "flutter_chat_core", "flutter_chat_types", "flutter_chat_ui", "google_fonts", "http", "iconsax", "material_symbols_icons", "pdf", "popover", "provider", "uuid", "visibility_detector", "web_socket_channel", "window_manager"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "4.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "fl_chart", "version": "1.0.0", "dependencies": ["equatable", "flutter", "vector_math"]}, {"name": "visibility_detector", "version": "0.4.0+2", "dependencies": ["flutter"]}, {"name": "material_symbols_icons", "version": "4.2815.1", "dependencies": ["args", "<PERSON><PERSON><PERSON>", "flutter", "glob", "path"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "flutter_chat_core", "version": "2.1.2", "dependencies": ["dio", "flutter", "freezed_annotation", "intl", "json_annotation", "path_provider"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "flutter_chat_types", "version": "3.6.2", "dependencies": ["equatable", "json_annotation", "meta"]}, {"name": "flutter_chat_ui", "version": "2.1.3", "dependencies": ["cross_cache", "diffutil_dart", "flutter", "flutter_chat_core", "provider", "scrollview_observer"]}, {"name": "window_manager", "version": "0.4.3", "dependencies": ["flutter", "path", "screen_retriever"]}, {"name": "pdf", "version": "3.11.3", "dependencies": ["archive", "barcode", "bidi", "crypto", "image", "meta", "path_parsing", "vector_math", "xml"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "popover", "version": "0.3.1", "dependencies": ["flutter"]}, {"name": "iconsax", "version": "0.0.8", "dependencies": ["flutter"]}, {"name": "http", "version": "1.3.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "file_picker", "version": "9.0.2", "dependencies": ["cross_file", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "4.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "<PERSON><PERSON><PERSON>", "version": "3.0.5", "dependencies": ["args"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "freezed_annotation", "version": "3.0.0", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "scrollview_observer", "version": "1.26.0", "dependencies": ["flutter"]}, {"name": "diffutil_dart", "version": "4.0.1", "dependencies": []}, {"name": "cross_cache", "version": "1.0.2", "dependencies": ["cross_file", "crypto", "dio", "flutter", "idb_shim", "path_provider"]}, {"name": "screen_retriever", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_linux", "screen_retriever_macos", "screen_retriever_platform_interface", "screen_retriever_windows"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "bidi", "version": "2.0.13", "dependencies": []}, {"name": "barcode", "version": "2.2.9", "dependencies": ["meta", "qr"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "win32", "version": "5.11.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.27", "dependencies": ["flutter"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.15", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "idb_shim", "version": "2.6.5+1", "dependencies": ["collection", "meta", "path", "sembast", "web"]}, {"name": "screen_retriever_windows", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "screen_retriever_platform_interface", "version": "0.2.0", "dependencies": ["flutter", "json_annotation", "plugin_platform_interface"]}, {"name": "screen_retriever_macos", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "screen_retriever_linux", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "qr", "version": "3.0.2", "dependencies": ["meta"]}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "sembast", "version": "3.8.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}], "configVersion": 1}