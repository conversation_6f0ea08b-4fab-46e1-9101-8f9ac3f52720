from fastapi import FastAPI
from fastapi.responses import JSONResponse

# Simple test server without complex dependencies
app = FastAPI(
    title="Hadith IQ Test Server",
    description="Simple test API for Hadith IQ Project",
    version="1.0.0",
)

@app.get("/")
async def root():
    return {"message": "Hadith IQ Test Server is running!"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "Server is running properly"}

@app.get("/api/test")
async def test_endpoint():
    return {
        "message": "Test endpoint working",
        "backend": "FastAPI",
        "status": "operational"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
